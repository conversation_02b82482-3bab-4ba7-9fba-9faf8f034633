{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Platform Config Schema", "type": "object", "required": ["name", "modified", "config"], "properties": {"name": {"type": "string", "description": "Name of the platform or 'base' for base config"}, "modified": {"type": "string", "format": "date-time", "description": "ISO 8601 date-time when config was last modified"}, "config": {"type": "object", "description": "Configuration object containing platform-specific settings", "properties": {"app": {"type": "object", "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "environment": {"type": "string"}, "bundleId": {"type": "string"}, "packageName": {"type": "string"}, "minVersion": {"type": "string"}, "minSdkVersion": {"type": "integer"}, "targetSdkVersion": {"type": "integer"}}}, "api": {"type": "object", "properties": {"baseUrl": {"type": "string", "format": "uri"}, "timeout": {"type": "integer", "minimum": 0}, "retries": {"type": "integer", "minimum": 0}}}, "features": {"type": "object", "additionalProperties": {"type": "boolean"}}, "ui": {"type": "object", "properties": {"theme": {"type": "string"}, "primaryColor": {"type": "string"}, "secondaryColor": {"type": "string"}, "statusBarStyle": {"type": "string"}, "statusBarColor": {"type": "string"}, "navigationBarStyle": {"type": "string"}}}, "platform": {"type": "object", "properties": {"storeUrl": {"type": "string", "format": "uri"}, "reviewPrompt": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "string"}}}}}}}}