# Platform Configs

## Summary
Build a config file for each platform that uses a file heiracary to build a final config file per platform.  The base config file will be the same for all platforms and then each platform will have a config file that will override or extend the base config file. The result will be a final config file that is specific to each platform.  

The files will be in JSON format. The naming of the input files will be as follows: input.base.config.json and input.[platform].config.json.  The platform will be the name of the platform.  The output config file will be in the output directory and named [platform].config.json.  Example: ios.config.json.

## Requirements
1. The config file will be in JSON format.
2. The config file will be in the same directory as the platform file.
3. The config file will be named [platform].config.json.
4. The config file will be a JSON object.
5. The config file will have a "name" property that is the name of the platform.
6. The config file will have a "modified" property that is the date the config file was last modified.
7. The config file will have a "config" property that is a JSON object that contains the config for the platform.
8. The bases config and the platform specific config will be validated using a JSON schema.
9. The configs will be merged together to create a final config file for each platform using a heiracary.  The base config will be the first in the heiracary and the platform specific config will be the last.  The platform specific config will override the base config.
10. The merged configs will written to an output directory as the input and named [platform].config.json.
11. Output a stats.md file the logs of the merge process and time it took to merge the configs as well as any errors that occurred.

Directory Structure

```
platform-configs
├── input.base.config.json
├── input.ios.config.json
├── input.android.config.json
├── output
│   ├── ios.config.json
│   ├── android.config.json
│   └── stats.md
└── schema.json
```

