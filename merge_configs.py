#!/usr/bin/env python3
"""
Platform Config Merger

This script merges base and platform-specific configuration files,
validates them against a JSON schema, and outputs merged configs
with statistics.
"""

import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Tuple
import copy

try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False
    print("Warning: jsonschema not available. Install with: pip install jsonschema")


class ConfigMerger:
    def __init__(self, schema_path: str = "schema.json"):
        self.schema_path = schema_path
        self.schema = None
        self.stats = {
            "start_time": None,
            "end_time": None,
            "duration": None,
            "processed_platforms": [],
            "errors": [],
            "warnings": []
        }
        
        # Load schema if available
        if os.path.exists(schema_path):
            try:
                with open(schema_path, 'r') as f:
                    self.schema = json.load(f)
            except Exception as e:
                self.stats["errors"].append(f"Failed to load schema: {e}")

    def deep_merge(self, base: Dict[Any, Any], override: Dict[Any, Any]) -> Dict[Any, Any]:
        """
        Deep merge two dictionaries. Override values take precedence.
        """
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.deep_merge(result[key], value)
            else:
                result[key] = copy.deepcopy(value)
        
        return result

    def validate_config(self, config: Dict[Any, Any], config_name: str) -> bool:
        """
        Validate a config against the JSON schema.
        """
        if not self.schema or not JSONSCHEMA_AVAILABLE:
            self.stats["warnings"].append(f"Schema validation skipped for {config_name}")
            return True
        
        try:
            jsonschema.validate(config, self.schema)
            return True
        except jsonschema.ValidationError as e:
            self.stats["errors"].append(f"Validation failed for {config_name}: {e.message}")
            return False
        except Exception as e:
            self.stats["errors"].append(f"Validation error for {config_name}: {e}")
            return False

    def load_config(self, file_path: str) -> Tuple[Dict[Any, Any], bool]:
        """
        Load and validate a config file.
        """
        try:
            with open(file_path, 'r') as f:
                config = json.load(f)
            
            # Validate the config
            is_valid = self.validate_config(config, file_path)
            
            return config, is_valid
        except FileNotFoundError:
            self.stats["errors"].append(f"Config file not found: {file_path}")
            return {}, False
        except json.JSONDecodeError as e:
            self.stats["errors"].append(f"Invalid JSON in {file_path}: {e}")
            return {}, False
        except Exception as e:
            self.stats["errors"].append(f"Error loading {file_path}: {e}")
            return {}, False

    def merge_platform_config(self, platform: str) -> bool:
        """
        Merge base config with platform-specific config.
        """
        base_config, base_valid = self.load_config("input.base.config.json")
        platform_config, platform_valid = self.load_config(f"input.{platform}.config.json")
        
        if not base_valid or not platform_valid:
            return False
        
        # Merge configs
        merged_config = self.deep_merge(base_config, platform_config)
        
        # Update metadata
        merged_config["name"] = platform
        merged_config["modified"] = datetime.now().isoformat() + "Z"
        
        # Validate merged config
        if not self.validate_config(merged_config, f"merged-{platform}"):
            return False
        
        # Write output
        output_path = f"output/{platform}.config.json"
        try:
            os.makedirs("output", exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(merged_config, f, indent=2)
            
            self.stats["processed_platforms"].append({
                "platform": platform,
                "output_file": output_path,
                "success": True
            })
            return True
            
        except Exception as e:
            self.stats["errors"].append(f"Failed to write {output_path}: {e}")
            return False

    def discover_platforms(self) -> List[str]:
        """
        Discover available platform configs by scanning input files.
        """
        platforms = []
        for file in os.listdir("."):
            if file.startswith("input.") and file.endswith(".config.json") and not file.startswith("input.base."):
                platform = file.replace("input.", "").replace(".config.json", "")
                platforms.append(platform)
        return platforms

    def generate_stats_report(self) -> str:
        """
        Generate a markdown stats report.
        """
        duration_str = f"{self.stats['duration']:.2f} seconds" if self.stats['duration'] else "Unknown"
        
        report = f"""# Config Merge Statistics

## Summary
- **Start Time**: {self.stats['start_time']}
- **End Time**: {self.stats['end_time']}
- **Duration**: {duration_str}
- **Platforms Processed**: {len(self.stats['processed_platforms'])}
- **Errors**: {len(self.stats['errors'])}
- **Warnings**: {len(self.stats['warnings'])}

## Processed Platforms
"""
        
        for platform_info in self.stats['processed_platforms']:
            status = "✅ Success" if platform_info['success'] else "❌ Failed"
            report += f"- **{platform_info['platform']}**: {status} → `{platform_info['output_file']}`\n"
        
        if self.stats['errors']:
            report += "\n## Errors\n"
            for i, error in enumerate(self.stats['errors'], 1):
                report += f"{i}. {error}\n"
        
        if self.stats['warnings']:
            report += "\n## Warnings\n"
            for i, warning in enumerate(self.stats['warnings'], 1):
                report += f"{i}. {warning}\n"
        
        return report

    def run(self) -> bool:
        """
        Run the config merger for all discovered platforms.
        """
        self.stats['start_time'] = datetime.now().isoformat()
        start_time = time.time()
        
        print("🚀 Starting config merge process...")
        
        # Discover platforms
        platforms = self.discover_platforms()
        print(f"📱 Discovered platforms: {', '.join(platforms)}")
        
        if not platforms:
            self.stats['errors'].append("No platform configs found")
            return False
        
        # Process each platform
        success_count = 0
        for platform in platforms:
            print(f"🔄 Processing {platform}...")
            if self.merge_platform_config(platform):
                print(f"✅ {platform} config merged successfully")
                success_count += 1
            else:
                print(f"❌ Failed to merge {platform} config")
        
        # Calculate duration and generate stats
        end_time = time.time()
        self.stats['end_time'] = datetime.now().isoformat()
        self.stats['duration'] = end_time - start_time
        
        # Write stats report
        stats_report = self.generate_stats_report()
        try:
            with open("output/stats.md", 'w') as f:
                f.write(stats_report)
            print("📊 Stats report written to output/stats.md")
        except Exception as e:
            print(f"⚠️ Failed to write stats report: {e}")
        
        print(f"🏁 Process completed in {self.stats['duration']:.2f} seconds")
        print(f"📈 Successfully processed {success_count}/{len(platforms)} platforms")
        
        return success_count == len(platforms)


if __name__ == "__main__":
    merger = ConfigMerger()
    success = merger.run()
    exit(0 if success else 1)
