# Platform Config Merger

A Python script that merges base and platform-specific configuration files, validates them against a JSON schema, and outputs merged configs with detailed statistics.

## Features

- 🔄 **Hierarchical Config Merging**: Merges base config with platform-specific overrides
- ✅ **JSON Schema Validation**: Validates all configs against a defined schema
- 📊 **Detailed Statistics**: Generates comprehensive merge reports
- 🚀 **Auto-Discovery**: Automatically discovers available platform configs
- 🛡️ **Error Handling**: Robust error handling with detailed logging

## Directory Structure

```
platform-configs/
├── input.base.config.json      # Base configuration (shared across platforms)
├── input.ios.config.json       # iOS-specific configuration
├── input.android.config.json   # Android-specific configuration
├── schema.json                 # JSON schema for validation
├── merge_configs.py            # Main merger script
├── output/                     # Generated output directory
│   ├── ios.config.json        # Merged iOS configuration
│   ├── android.config.json    # Merged Android configuration
│   └── stats.md               # Merge statistics report
└── README.md                  # This file
```

## Usage

### Prerequisites

Install the required Python package for JSON schema validation:

```bash
pip install jsonschema
```

### Running the Merger

Simply run the main script:

```bash
python3 merge_configs.py
```

The script will:
1. Discover all platform configs (files matching `input.*.config.json` except base)
2. Load and validate the base config
3. For each platform:
   - Load and validate the platform-specific config
   - Merge it with the base config (platform settings override base)
   - Validate the merged result
   - Write the output to `output/{platform}.config.json`
4. Generate a detailed statistics report in `output/stats.md`

### Configuration Format

All config files must follow this structure:

```json
{
  "name": "platform-name",
  "modified": "2025-06-26T00:00:00Z",
  "config": {
    "app": { /* app-specific settings */ },
    "api": { /* API configuration */ },
    "features": { /* feature flags */ },
    "ui": { /* UI configuration */ },
    "platform": { /* platform-specific settings */ }
  }
}
```

### Adding New Platforms

To add a new platform:

1. Create a new config file: `input.{platform}.config.json`
2. Follow the JSON schema structure
3. Run the merger script

The script will automatically discover and process the new platform.

### Schema Validation

The `schema.json` file defines the structure and validation rules for all configs. The schema supports:

- Required fields validation
- Type checking
- Format validation (URLs, dates)
- Nested object validation

## Example Output

The merger produces clean, validated JSON files and a comprehensive stats report:

```markdown
# Config Merge Statistics

## Summary
- **Start Time**: 2025-06-26T11:07:35.664559
- **End Time**: 2025-06-26T11:07:35.665439
- **Duration**: 0.01 seconds
- **Platforms Processed**: 2
- **Errors**: 0
- **Warnings**: 0

## Processed Platforms
- **android**: ✅ Success → `output/android.config.json`
- **ios**: ✅ Success → `output/ios.config.json`
```

## Automated Deployment

### GitHub Actions Integration

The system includes automated deployment via GitHub Actions that:

- **Triggers** on PR merges to deployment branches (`prod-deploy`, `qa-deploy`, `dev-deploy`)
- **Merges** and validates all platform configs automatically
- **Uploads** configs to environment-specific S3 buckets
- **Provides** detailed deployment reports and verification

#### Quick Setup
1. Set up AWS S3 buckets for each environment
2. Configure GitHub repository secrets (AWS credentials and bucket names)
3. Merge PRs to deployment branches to trigger automated deployment

📖 **See [GITHUB_ACTIONS_SETUP.md](GITHUB_ACTIONS_SETUP.md) for complete setup instructions**

### Manual Usage

For local development and testing, run the merger manually:

```bash
python3 merge_configs.py
```

## Development

### Testing

The system includes comprehensive validation:

```bash
# Test JSON schema validation
python3 -c "
import json, jsonschema
with open('schema.json') as f: schema = json.load(f)
with open('input.base.config.json') as f: config = json.load(f)
jsonschema.validate(config, schema)
print('✅ Validation successful')
"
```

### Extending

The system is designed to be easily extensible:

- **Add new platforms**: Create new `input.{platform}.config.json` files
- **Extend schema**: Modify `schema.json` to support new configuration options
- **Custom merge logic**: Modify the merger script for specialized merge strategies
- **New environments**: Add deployment branches and S3 bucket configuration

## Error Handling

The script provides comprehensive error handling:

- Invalid JSON files are reported with specific error messages
- Schema validation failures include detailed validation errors
- File I/O errors are caught and logged
- The process continues even if individual platforms fail
- GitHub Actions workflow includes verification steps and rollback capabilities

All errors and warnings are logged in the statistics report for easy debugging.
