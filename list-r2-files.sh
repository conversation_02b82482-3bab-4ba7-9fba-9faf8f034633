#!/bin/bash

# List files in R2 buckets using Wrangler CLI
# This script helps verify that files were uploaded correctly

echo "🔍 Listing files in R2 buckets..."
echo ""

# Check if wrangler is available
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI not found. Install with: npm install -g wrangler"
    exit 1
fi

# Function to check files in a bucket
check_bucket() {
    local bucket_name=$1
    local env_name=$2
    
    echo "📦 Checking bucket: $bucket_name ($env_name)"
    echo "----------------------------------------"
    
    # Try to get each expected file
    files=("android.config.json" "ios.config.json" "stats.md")
    
    for file in "${files[@]}"; do
        echo -n "🔍 $file: "
        if wrangler r2 object get "$bucket_name/configs/$env_name/$file" --file "/tmp/check_$file" > /dev/null 2>&1; then
            size=$(stat -c%s "/tmp/check_$file" 2>/dev/null || stat -f%z "/tmp/check_$file" 2>/dev/null || echo "unknown")
            echo "✅ Found ($size bytes)"
            rm -f "/tmp/check_$file"
        else
            echo "❌ Not found"
        fi
    done
    echo ""
}

# Check all three buckets
check_bucket "brainivy-configs-dev" "development"
check_bucket "brainivy-configs-qa" "qa"
check_bucket "brainivy-configs-prod" "production"

echo "💡 If files show as 'Found' but aren't visible in the dashboard:"
echo "   1. Try refreshing the Cloudflare dashboard"
echo "   2. Navigate into the bucket and look for 'configs' folder"
echo "   3. The dashboard sometimes has display delays"
echo ""
echo "🌐 If public access is enabled, files should be accessible at:"
echo "   https://pub-[hash].r2.dev/configs/[environment]/[filename]"
