# Environment Configuration for Platform Config Deployment
# This file documents the environment-specific settings used in the GitHub Actions workflow

environments:
  production:
    branch: prod-deploy
    r2_bucket: brainivy-configs-prod
    r2_path: configs/production
    description: Production environment for live applications

  qa:
    branch: qa-deploy
    r2_bucket: brainivy-configs-qa
    r2_path: configs/qa
    description: QA environment for testing and validation

  development:
    branch: dev-deploy
    r2_bucket: brainivy-configs-dev
    r2_path: configs/development
    description: Development environment for ongoing development

# Required GitHub Secrets:
required_secrets:
  # Cloudflare Configuration
  - CLOUDFLARE_API_TOKEN   # Cloudflare API token with R2 permissions
  - CLOUDFLARE_ACCOUNT_ID  # Cloudflare account ID

# R2 Bucket Structure:
# Each bucket will have the following structure:
# bucket-name/
# └── configs/
#     └── {environment}/
#         ├── ios.config.json
#         ├── android.config.json
#         └── stats.md

# Cloudflare API Token Requirements:
# The API token needs the following permissions:
# - Account: Cloudflare R2:Edit
# - Zone Resources: Include All zones (if using custom domains)
#
# To create the token:
# 1. Go to https://dash.cloudflare.com/profile/api-tokens
# 2. Click "Create Token"
# 3. Use "Custom token" template
# 4. Add permissions: Account:Cloudflare R2:Edit
# 5. Add account resource: Include your account
# 6. Continue and create token

# Public Access:
# Each bucket should have public access enabled for the configs to be served publicly.
# This can be configured in the Cloudflare dashboard under R2 > Bucket > Settings > Public Development URL
