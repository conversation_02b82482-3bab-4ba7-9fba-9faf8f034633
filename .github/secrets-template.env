# GitHub Secrets Template for Platform Config Deployment
# Copy this file and fill in your actual values, then add them as GitHub repository secrets

# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ACCOUNT_ID=e59a460617f2a48be73bb62130419458

# R2 Buckets (already created):
# - brainivy-configs-prod
# - brainivy-configs-qa
# - brainivy-configs-dev

# How to get your Cloudflare API Token:
# 1. Go to https://dash.cloudflare.com/profile/api-tokens
# 2. Click "Create Token"
# 3. Use "Custom token" template
# 4. Add permissions: Account:Cloudflare R2:Edit
# 5. Add account resource: Include your account
# 6. Continue and create token

# Example values:
# CLOUDFLARE_API_TOKEN=abc123def456ghi789jkl012mno345pqr678stu
# CLOUDFLARE_ACCOUNT_ID=e59a460617f2a48be73bb62130419458
