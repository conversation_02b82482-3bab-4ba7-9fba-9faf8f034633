# Configure R2 Public Access

Since the Cloudflare API tools don't include bucket public access configuration, you'll need to enable public access through the Cloudflare dashboard.

## Steps to Enable Public Access

### For Development/Testing (r2.dev subdomain)

For each bucket (`brainivy-configs-prod`, `brainivy-configs-qa`, `brainivy-configs-dev`):

1. **Go to Cloudflare Dashboard**: https://dash.cloudflare.com
2. **Navigate to R2**: Click on "R2 Object Storage" in the left sidebar
3. **Select Bucket**: Click on the bucket name (e.g., `brainivy-configs-prod`)
4. **Go to Settings**: Click the "Settings" tab
5. **Enable Public Development URL**:
   - Under "Public Development URL", click **Enable**
   - In the confirmation dialog, type `allow` and click **Allow**
6. **Note the Public URL**: Copy the public URL (format: `https://pub-[hash].r2.dev`)

### For Production (Custom Domain - Optional)

If you want to use custom domains instead of r2.dev:

1. **Add Domain to Cloudflare**: Ensure your domain is managed by Cloudflare
2. **Connect Custom Domain**:
   - In bucket settings, under "Custom Domains", click **Add**
   - Enter your subdomain (e.g., `configs-prod.yourdomain.com`)
   - Click **Continue** and **Connect Domain**

## Expected Public URLs

After enabling public access, your buckets will be accessible at:

- **Production**: `https://pub-[hash].r2.dev` (or custom domain)
- **QA**: `https://pub-[hash].r2.dev` (or custom domain)  
- **Development**: `https://pub-[hash].r2.dev` (or custom domain)

## File Access Examples

Once public access is enabled, your config files will be accessible at:

```
https://pub-[hash].r2.dev/configs/production/ios.config.json
https://pub-[hash].r2.dev/configs/production/android.config.json
https://pub-[hash].r2.dev/configs/production/stats.md
```

## Security Considerations

- **r2.dev URLs**: Rate limited, intended for development only
- **Custom Domains**: Recommended for production, allows WAF, caching, access controls
- **File Permissions**: All files in public buckets are readable by anyone with the URL

## Next Steps

1. Enable public access for all three buckets
2. Note down the public URLs
3. Update the GitHub Actions workflow with the bucket names and URLs
4. Test the deployment workflow

## Automation Note

The GitHub Actions workflow will upload files to these buckets, and they'll be immediately accessible via the public URLs once uploaded.
