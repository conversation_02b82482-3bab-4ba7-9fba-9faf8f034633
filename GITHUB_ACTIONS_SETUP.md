# GitHub Actions Setup Guide

This guide explains how to set up the automated platform config deployment using GitHub Actions and Cloudflare R2.

## Overview

The GitHub Actions workflow automatically:
1. **Triggers** on PR merges to deployment branches (`prod-deploy`, `qa-deploy`, `dev-deploy`)
2. **Merges** base and platform-specific configs using the Python merger script
3. **Validates** all configurations against the JSON schema
4. **Uploads** merged configs to environment-specific Cloudflare R2 buckets
5. **Verifies** successful deployment and provides detailed reporting

## Workflow Triggers

The workflow runs when:
- **Pull Requests are merged** into deployment branches
- **Direct pushes** are made to deployment branches

### Deployment Branches
- `prod-deploy` → Production environment
- `qa-deploy` → QA environment  
- `dev-deploy` → Development environment

## Prerequisites

### 1. Cloudflare R2 Setup

#### R2 Buckets (Already Created)
The following R2 buckets have been created in your Cloudflare account:
```
brainivy-configs-prod    # Production environment
brainivy-configs-qa      # QA environment
brainivy-configs-dev     # Development environment
```

#### Enable Public Access
For each bucket, enable public access so configs can be served publicly:

1. **Go to Cloudflare Dashboard**: https://dash.cloudflare.com
2. **Navigate to R2**: Click "R2 Object Storage" in the left sidebar
3. **Select Bucket**: Click on the bucket name
4. **Go to Settings**: Click the "Settings" tab
5. **Enable Public Development URL**:
   - Under "Public Development URL", click **Enable**
   - Type `allow` in the confirmation dialog and click **Allow**
6. **Note the Public URL**: Copy the public URL (format: `https://pub-[hash].r2.dev`)

#### API Token Setup
Create a Cloudflare API token with R2 permissions:

1. Go to https://dash.cloudflare.com/profile/api-tokens
2. Click **Create Token**
3. Use **Custom token** template
4. Add permissions: **Account:Cloudflare R2:Edit**
5. Add account resource: **Include your account**
6. Continue and create token

### 2. GitHub Repository Secrets

Add the following secrets to your GitHub repository:

#### Required Secrets
| Secret Name | Description | Example |
|-------------|-------------|---------|
| `CLOUDFLARE_API_TOKEN` | Cloudflare API token with R2 permissions | `abc123def456ghi789...` |
| `CLOUDFLARE_ACCOUNT_ID` | Your Cloudflare account ID | `e59a460617f2a48be73bb62130419458` |

#### Adding Secrets
1. Go to your GitHub repository
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add each secret from the table above

## Workflow Steps

### 1. Environment Detection
The workflow automatically detects the target environment based on the branch:
- `prod-deploy` → Production
- `qa-deploy` → QA
- `dev-deploy` → Development

### 2. Config Validation
- Validates all input config files exist
- Checks JSON schema compliance
- Ensures required files are present

### 3. Config Merging
- Runs the Python merger script
- Merges base config with platform-specific configs
- Generates timestamped output files

### 4. Output Verification
- Verifies output directory and files exist
- Validates generated config files
- Checks statistics report generation

### 5. R2 Deployment
- Installs Cloudflare Wrangler CLI
- Configures Cloudflare credentials
- Uploads configs to environment-specific R2 buckets
- Verifies successful upload

### 6. Deployment Summary
- Creates detailed deployment summary
- Shows deployed configs and statistics
- Provides R2 bucket and public URL information

## R2 Structure

Configs are uploaded to R2 with the following structure:

```
r2://bucket-name/
└── configs/
    └── {environment}/
        ├── ios.config.json
        ├── android.config.json
        └── stats.md
```

### Example Paths
- **Production**: `brainivy-configs-prod/configs/production/`
- **QA**: `brainivy-configs-qa/configs/qa/`
- **Development**: `brainivy-configs-dev/configs/development/`

### Public URLs
Once public access is enabled, configs will be accessible at:
- **Production**: `https://pub-[hash].r2.dev/configs/production/ios.config.json`
- **QA**: `https://pub-[hash].r2.dev/configs/qa/ios.config.json`
- **Development**: `https://pub-[hash].r2.dev/configs/development/ios.config.json`

## Usage Examples

### Deploying to Development
1. Create a PR targeting `dev-deploy` branch
2. Merge the PR
3. Workflow automatically runs and deploys to development R2 bucket

### Deploying to Production
1. Create a PR targeting `prod-deploy` branch
2. Get PR reviewed and approved
3. Merge the PR
4. Workflow automatically runs and deploys to production R2 bucket

## Monitoring and Troubleshooting

### Viewing Workflow Runs
1. Go to your GitHub repository
2. Click the **Actions** tab
3. Select the "Deploy Platform Configs" workflow
4. View individual run details and logs

### Common Issues

#### Authentication Errors
- **Cause**: Invalid Cloudflare API token
- **Solution**: Verify Cloudflare API token and account ID are correctly set in GitHub secrets

#### R2 Upload Failures
- **Cause**: Insufficient R2 permissions or invalid bucket names
- **Solution**: Check API token permissions and verify bucket names exist

#### Config Validation Errors
- **Cause**: Invalid JSON or schema violations
- **Solution**: Review config files and ensure they match the schema

#### Missing Platform Configs
- **Cause**: No platform config files found
- **Solution**: Ensure `input.*.config.json` files exist (except base)

### Workflow Outputs

The workflow provides detailed outputs:
- **Step-by-step logs** for debugging
- **Deployment summary** with config details
- **R2 verification** confirming successful uploads
- **Public URL information** for accessing deployed configs
- **Statistics report** showing merge details

## Security Considerations

1. **Cloudflare API Token**: Store as GitHub secrets, never in code
2. **R2 Bucket Access**: Use least-privilege API token permissions
3. **Public Access**: Configs are publicly accessible once deployed
4. **Branch Protection**: Consider requiring PR reviews for deployment branches
5. **Environment Separation**: Use separate buckets for each environment

## Customization

### Adding New Environments
1. Create a new deployment branch (e.g., `staging-deploy`)
2. Add the branch to the workflow trigger
3. Add environment detection logic
4. Create corresponding R2 bucket

### Modifying R2 Paths
Edit the `r2_path` values in the workflow's environment detection step.

### Adding Notifications
Consider adding Slack, email, or other notification steps to the workflow for deployment alerts.
